"use client"

import { useAuth } from "@/components/providers/auth-provider"
import { usePermissions } from "@/lib/hooks/use-permissions"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export default function DebugAuthPage() {
  const { user, session, isLoading, signOut } = useAuth()
  const permissions = usePermissions()

  const testPermissions = [
    "audits:read",
    "audits:create", 
    "audits:update",
    "audits:delete",
    "organizations:read",
    "organizations:create",
    "organizations:update", 
    "organizations:delete",
    "users:read",
    "users:create",
    "users:update",
    "users:delete",
    "reports:read",
    "reports:create",
    "observations:read",
    "observations:create"
  ]

  if (isLoading) {
    return <div className="p-8">Chargement...</div>
  }

  return (
    <div className="container mx-auto p-8 space-y-6">
      <h1 className="text-3xl font-bold">Debug Authentication & Permissions</h1>
      
      {/* Session Info */}
      <Card>
        <CardHeader>
          <CardTitle>Session Information</CardTitle>
          <CardDescription>Current user session details</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {user ? (
            <div className="space-y-2">
              <div><strong>ID:</strong> {user.id}</div>
              <div><strong>Email:</strong> {user.email}</div>
              <div><strong>Name:</strong> {user.name || 'N/A'}</div>
              <div><strong>Role:</strong> <Badge variant="outline">{user.role}</Badge></div>
              <div><strong>Active:</strong> {user.isActive ? '✅' : '❌'}</div>
              <div><strong>Email Verified:</strong> {user.emailVerified ? '✅' : '❌'}</div>
            </div>
          ) : (
            <div className="text-red-600">No user session found</div>
          )}
          
          <Button onClick={signOut} variant="destructive" size="sm">
            Sign Out
          </Button>
        </CardContent>
      </Card>

      {/* Permissions */}
      <Card>
        <CardHeader>
          <CardTitle>Permissions Check</CardTitle>
          <CardDescription>Testing various permissions for current user</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
            {testPermissions.map((permission) => {
              const hasPermission = permissions.checkPermission(permission)
              return (
                <div
                  key={permission}
                  className={`p-2 rounded text-sm ${
                    hasPermission 
                      ? 'bg-green-100 text-green-800 border border-green-200' 
                      : 'bg-red-100 text-red-800 border border-red-200'
                  }`}
                >
                  {hasPermission ? '✅' : '❌'} {permission}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Role Checks */}
      <Card>
        <CardHeader>
          <CardTitle>Role Checks</CardTitle>
          <CardDescription>Boolean role checks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className={`p-3 rounded ${permissions.isSuperAdmin ? 'bg-green-100' : 'bg-gray-100'}`}>
              <div className="font-medium">Super Admin</div>
              <div>{permissions.isSuperAdmin ? '✅' : '❌'}</div>
            </div>
            <div className={`p-3 rounded ${permissions.isAdmin ? 'bg-green-100' : 'bg-gray-100'}`}>
              <div className="font-medium">Admin</div>
              <div>{permissions.isAdmin ? '✅' : '❌'}</div>
            </div>
            <div className={`p-3 rounded ${permissions.isManager ? 'bg-green-100' : 'bg-gray-100'}`}>
              <div className="font-medium">Manager</div>
              <div>{permissions.isManager ? '✅' : '❌'}</div>
            </div>
            <div className={`p-3 rounded ${permissions.isAuditor ? 'bg-green-100' : 'bg-gray-100'}`}>
              <div className="font-medium">Auditor</div>
              <div>{permissions.isAuditor ? '✅' : '❌'}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Raw Data */}
      <Card>
        <CardHeader>
          <CardTitle>Raw Session Data</CardTitle>
          <CardDescription>Complete session object for debugging</CardDescription>
        </CardHeader>
        <CardContent>
          <pre className="bg-gray-100 p-4 rounded-md overflow-auto text-sm">
            {JSON.stringify({ session, user }, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  )
}
